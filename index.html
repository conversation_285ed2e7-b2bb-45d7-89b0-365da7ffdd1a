<!DOCTYPE html><html lang="zh-CN"><head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Cache-Control" content="no-cache">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0,minimal-ui:ios" name="viewport">
    <title>{$zt_name}</title>
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/style.min.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/animate.css">
    <link rel="stylesheet" href="https://ztimg.hefei.cc/static/common/css/<EMAIL>">
    <link rel="stylesheet" href="css/index.css?v={php} echo mt_rand(1000,999999);{/php}">
    <script defer="" src="css/polyfill.js"></script>
    <!-- <script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script> 
    <script> var vConsole = new window.VConsole(); </script> -->
    <style>
        [v-cloak] {
            display: none;
        }
    </style>
</head>

<body>
    <div id="app" class="warp" v-cloak="">
        <!-- <div class="musicbtn" :class="on?'on':''" @click="bgClick" v-if="page"></div> -->
        <!-- 首页 -->
        <div class="page fc">
            <img src="img/title2_1.png" class="title animate__animated animate__zoomIn">
            <img src="img/logo2.png" class="logo">
            <div class="area" v-if="show===0">
                <input type="text" v-model="phone" placeholder="请输入您的手机号进入活动页面">
                <img src="img/button1.png" class="button1" @click="submit">
            </div>
            <div class="area" v-if="show===1">
                <div class="button4" v-if="scene===2">
                    <wx-open-launch-weapp  id="launch-btn" appid="wx2d6b169da71ccff7" :path="link" style="height: 100%;width: 100%;display:block;position: absolute;">
                        <component :is="'script'" type="text/wxtag-template">
                            <button class="btn" style="width: 400px;height:800px;background-color: #fff;opacity: 0;">去抽奖</button>
                        </component>
                    </wx-open-launch-weapp>
                </div>
                <div class="button4" v-if="scene===1"  @click="goPrize"></div>
            </div>
        </div>
    </div>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/<EMAIL>"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/jquery-3.6.0.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/libs/howler.min.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook1.js"></script>
    <script src="https://ztimg.hefei.cc/static/common/js/hook/hook2.js"></script>
    <script>
        const {ref ,computed,watch,createApp } = Vue
        const app = createApp({
            setup() {
                // const { on, bgClick } = useBgMusic('https://ztimg.hefei.cc/zt2024/trkndl2025/122.mp3')
                const page = ref(1)
                const show = ref(0)
                const link = ref('')
                const goPrize = ()=>{
                    wx.miniProgram.reLaunch( { url : `/${link.value}` } )
                }
                const scene = ref(0)
                var u = navigator.userAgent;
                if(u.indexOf("miniProgram") != -1){
                    //在微信小程序
                    scene.value = 1
                }else {
                    //在微信网页
                    scene.value = 2
                }
                const phone = ref('')
                const reload = ()=>{
                    window.location.reload()
                }
                const submit = throttle(()=>{
                    defaultHttp('config',{phone:phone.value}).then((res)=>{
                        if (res.status === 1) {
                            link.value = res.data.path
                            show.value = 1
                        }else {
                            vantAlert(res.msg, reload)
                        }
                    })
                })

                return {
                    // on, bgClick,
                    phone,
                    page, show,submit,
                    link,scene,goPrize,
                }
            }
        })
        app.use(vant);
        app.mount('#app');
    </script>

{include file="xjqlx/share"/}

</body>
</html>