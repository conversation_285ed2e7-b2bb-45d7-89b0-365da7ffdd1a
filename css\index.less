// 取消微信浏览器点击的蓝色背景
// *{
//     -webkit-touch-callout:none;
//     -webkit-user-select:none; 
//     -moz-user-select:none;
//     -ms-user-select:none;
//     user-select:none;
//     -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
//     -webkit-user-select: none;
//     -moz-user-focus: none;
//     -moz-user-select: none
// }
body,div,h1,h2,h3,h4,h5,h6,html,li,p,span{font-size:3.5vw}
.musicbtn {
    width: 6vw;
    height: 6vw;
    right: 3vw;
    z-index: 8;
}

.warp {
    width: 100vw;
    // height: 100vh;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;

    .page {
        width: 100%;
        height: 100vh;
        min-height: 177vw;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        color: #000;
        background:#fcdb5a url(../img/bj_2.jpg)  no-repeat center top / 100% auto;    
        .bg2{
            width: 100vw;
            position: absolute;
            bottom: 0;
            pointer-events: none;
        } 
        .title{
            width: 80vw;
            position: absolute;
            top: 24vw;
        }
        .area{
            width: 86.6667vw;
            height: 39.6vw;
            position: absolute;
            bottom: 23vw;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            input{
                margin-top: 2vw;
                width: 69.3333vw;
                height: 9.3333vw;
                background-color: #c9c9c9;
                text-align: center;
            }
            .button1{
                margin-top: 4vw;
                width: 32vw;
            }
        }
    }


}

.fc {
    justify-content: center;
}
.logo{
    width: 30.8vw;
    position: absolute;
    top: 4vw;
    left: 4vw;
}
.mask {
    z-index: 8;
    position: fixed;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    min-height: 100vh;
    width: 100vw;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(2px);
    color: #d90011;
    .popup {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        .close {
            width: 9vw;
            height: 9vw;
            background: url(../img/close.png) no-repeat center top / 100% 100%;
            position: absolute;
            bottom: 0vw;
        }
    }


    .popup1 {
        width: 76.16vw;
        height: 67.52vw;
        background: url(../img/popup1.png) no-repeat center top / 100% 100%;
    }
    .poster{
        width: 77.3333vw;
    }
    .button4{
        margin-top: 20vw;
        width: 32vw;
        height: 9.2vw;
        background: url(../img/button2.png) no-repeat center top / 100% 100%;
        position: relative;
    }
}