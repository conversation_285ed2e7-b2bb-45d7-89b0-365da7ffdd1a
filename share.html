<script>
    var _hmt = _hmt || [];
    (function() {  var hm = document.createElement("script");  hm.src = "https://hm.baidu.com/hm.js?9f7085c3b7e7a927e229cd5894d20f34";
      var s = document.getElementsByTagName("script")[0];   s.parentNode.insertBefore(hm, s);  })();
</script>
{php} $cshu="https://" . $_SERVER['HTTP_HOST'] . urlencode($_SERVER['REQUEST_URI']); {/php}
<script src="//z.hefei.cc/wechat/index/jssdk.do?alias=4866e421a5af89b6&url={$cshu}"></script>
<script src="//res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
<script>
    window.shareData = {
        "imgUrl": "https://z.hfurl.cc{$share_img}",
        "sendFriendLink": "{$share_link|raw}",
        "tTitle": "{$share_title|raw}",
        "tContent":"{$share_desc|raw}"
    };

    function shareWeixin() {
        wx.config({
            debug: false,                       //为true时为调试模式,false为正常11
            appId: appid,
            timestamp: timestamp,
            nonceStr: nonceStr,
            signature: signature,
            jsApiList: [
                "updateTimelineShareData",
                "updateAppMessageShareData",
                "onMenuShareTimeline",
                "onMenuShareAppMessage",
                "onMenuShareQQ",
                "onMenuShareQZone",
                "onMenuShareWeibo",
                "openLocation",
            ],
            openTagList: ['wx-open-launch-weapp'] // 必填，需要使用的JS接口列表
        });

        wx.ready(function (res) {
            //分享给朋友
            wx.updateAppMessageShareData({
                title: window.shareData.tTitle, // 分享标题
                desc: window.shareData.tContent, // 分享描述
                link: window.shareData.sendFriendLink, // 分享链接
                imgUrl: window.shareData.imgUrl, // 分享图标
                success: function (res) {
                    // 用户确认分享后执行的回调函数
                    console.log('分享给朋友');
                },
                cancel: function () {
                    // 用户取消分享后执行的回调函
                }
            });
            //分享到朋友圈
            wx.updateTimelineShareData({
                title: window.shareData.tTitle, // 分享标题
                link: window.shareData.sendFriendLink, // 分享链接
                imgUrl: window.shareData.imgUrl, // 分享图标
                success: function (res) {
                    // 用户确认分享后执行的回调函数
                    console.log('分享到朋友圈');
                },
                cancel: function () {
                    // 用户取消分享后执行的回调函数
                }
            });
        });
    }
    window.addEventListener("load", shareWeixin);
</script>