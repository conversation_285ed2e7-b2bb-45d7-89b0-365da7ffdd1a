body,
div,
h1,
h2,
h3,
h4,
h5,
h6,
html,
li,
p,
span {
  font-size: 2.1vh;
}
.musicbtn {
  width: 3.6vh;
  height: 3.6vh;
  right: 1.8vh;
  z-index: 8;
}
.warp {
  width: 60vh;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}
.warp .page {
  width: 100%;
  height: 100vh;
  min-height: 106.1vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  color: #000;
  background: #fcdb5a url(../img/bj.jpg) no-repeat center top / 100% auto;
}
.warp .page .bg2 {
  width: 60vh;
  position: absolute;
  bottom: 0;
  pointer-events: none;
}
.warp .page .area {
  width: 52vh;
  height: 23.7vh;
  background: url(../img/area.png) no-repeat center top / 100% 100%;
  position: absolute;
  bottom: 13.8vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.warp .page .area input {
  margin-top: 1.2vh;
  width: 41.6vh;
  height: 5.6vh;
  background-color: #c9c9c9;
  text-align: center;
}
.warp .page .area .button1 {
  margin-top: 2.4vh;
  width: 19.2vh;
}
.fc {
  justify-content: center;
}
.logo {
  width: 18.5vh;
  position: absolute;
  top: 2.4vh;
  left: 2.4vh;
}
.mask {
  z-index: 8;
  position: fixed;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  min-height: 100vh;
  width: 60vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(2px);
  color: #d90011;
}
.mask .popup {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}
.mask .popup .close {
  width: 5.4vh;
  height: 5.4vh;
  background: url(../img/close.png) no-repeat center top / 100% 100%;
  position: absolute;
  bottom: 0vh;
}
.mask .popup1 {
  width: 45.7vh;
  height: 40.5vh;
  background: url(../img/popup1.png) no-repeat center top / 100% 100%;
}
.mask .poster {
  width: 46.4vh;
}
.mask .button4 {
  margin-top: 12vh;
  width: 19.2vh;
  height: 5.5vh;
  background: url(../img/button2.png) no-repeat center top / 100% 100%;
  position: relative;
}
